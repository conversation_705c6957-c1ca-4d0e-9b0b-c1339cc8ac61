Metadata-Version: 2.3
Name: cognite-sdk
Version: 7.73.4
Summary: Cognite Python SDK
License: Apache-2.0
Author: <PERSON><PERSON><PERSON> Vollset
Author-email: <EMAIL>
Requires-Python: >=3.10,<4.0
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Provides-Extra: all
Provides-Extra: functions
Provides-Extra: geo
Provides-Extra: numpy
Provides-Extra: pandas
Provides-Extra: pyodide
Provides-Extra: sympy
Provides-Extra: yaml
Requires-Dist: PyYAML (>=6.0,<7.0) ; extra == "yaml" or extra == "all"
Requires-Dist: geopandas (>=0.14) ; extra == "geo" or extra == "all"
Requires-Dist: msal (>=1.31,<2.0)
Requires-Dist: numpy (>=1.25) ; extra == "numpy" or extra == "all"
Requires-Dist: packaging (>=20)
Requires-Dist: pandas (>=2.1) ; extra == "pandas" or extra == "all"
Requires-Dist: pip (>=20.0.0) ; extra == "functions" or extra == "all"
Requires-Dist: protobuf (>=4)
Requires-Dist: pyodide-http (>=0.2.1,<0.3.0) ; extra == "pyodide"
Requires-Dist: requests (>=2.27,<3.0)
Requires-Dist: requests_oauthlib (>=1,<2)
Requires-Dist: shapely (>=1.7.0) ; extra == "geo" or extra == "all"
Requires-Dist: sympy ; extra == "sympy" or extra == "all"
Requires-Dist: typing_extensions (>=4)
Requires-Dist: tzdata (>=2024.1) ; (platform_system == "Windows" or platform_system == "Emscripten") and (extra == "pyodide")
Project-URL: Documentation, https://cognite-sdk-python.readthedocs-hosted.com
Description-Content-Type: text/markdown

<a href="https://cognite.com/">
    <img src="https://github.com/cognitedata/cognite-python-docs/blob/master/img/cognite_logo.png" alt="Cognite logo" title="Cognite" align="right" height="80" />
</a>

Cognite Python SDK
==========================
[![build](https://github.com/cognitedata/cognite-sdk-python/workflows/release/badge.svg)](https://github.com/cognitedata/cognite-sdk-python/actions?query=workflow:release)
[![Downloads](https://img.shields.io/pypi/dm/cognite-sdk)](https://pypistats.org/packages/cognite-sdk)
[![GitHub](https://img.shields.io/github/license/cognitedata/cognite-sdk-python)](https://github.com/cognitedata/cognite-sdk-python/blob/master/LICENSE)
[![codecov](https://codecov.io/gh/cognitedata/cognite-sdk-python/branch/master/graph/badge.svg)](https://codecov.io/gh/cognitedata/cognite-sdk-python)
[![Documentation Status](https://readthedocs.com/projects/cognite-sdk-python/badge/?version=latest)](https://cognite-sdk-python.readthedocs-hosted.com/en/latest/)
[![PyPI version](https://badge.fury.io/py/cognite-sdk.svg)](https://pypi.org/project/cognite-sdk/)
[![conda version](https://anaconda.org/conda-forge/cognite-sdk/badges/version.svg)](https://anaconda.org/conda-forge/cognite-sdk)
[![mypy](http://www.mypy-lang.org/static/mypy_badge.svg)](http://mypy-lang.org)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/ambv/black)

This is the Cognite Python SDK for developers and data scientists working with Cognite Data Fusion (CDF).
The package is tightly integrated with pandas, and helps you work easily and efficiently with data in Cognite Data Fusion (CDF).

## Reference documentation
* [SDK Documentation](https://cognite-sdk-python.readthedocs-hosted.com/en/latest/)
* [CDF API Documentation](https://doc.cognitedata.com/)
* [Cognite Developer Documentation](https://docs.cognite.com/dev/)

## Installation

### Without any optional dependencies
To install the core version of this package:
```bash
$ pip install cognite-sdk
```

### With optional dependencies
A number of optional dependencies may be specified in order to support a wider set of features.
The available extras (along with the libraries they include) are:
- numpy `[numpy]`
- pandas `[pandas]`
- geo `[geopandas, shapely]`
- sympy `[sympy]`
- functions `[pip]`
- yaml `[PyYAML]`
- all `[numpy, pandas, geopandas, shapely, sympy, pip, PyYAML]`

To include optional dependencies, specify them like this with pip:

```bash
$ pip install "cognite-sdk[pandas, geo]"
```

or like this if you are using poetry:
```bash
$ poetry add cognite-sdk -E pandas -E geo
```

### Performance notes
If you regularly need to fetch large amounts of datapoints, consider installing with `numpy`
(or with `pandas`, as it depends on `numpy`) for best performance, then use the `retrieve_arrays` (or `retrieve_dataframe`) endpoint(s). This avoids building large pure Python data structures, and instead reads data directly into memory-efficient `numpy.ndarrays`.

### Windows specific
On Windows, it is recommended to install `geopandas` and its dependencies using `conda` package manager, see [geopandas installation page](https://geopandas.org/en/stable/getting_started/install.html#installation).
The following commands create a new environment, install `geopandas` and `cognite-sdk`.

```bash
conda create -n geo_env
conda activate geo_env
conda install --channel conda-forge geopandas
pip install cognite-sdk
```

## Changelog
Wondering about upcoming or previous changes to the SDK? Take a look at the [CHANGELOG](https://github.com/cognitedata/cognite-sdk-python/blob/master/CHANGELOG.md).

## Migration Guide
To help you upgrade your code(base) quickly and safely to a newer major version of the SDK, check out our migration guide. It is a more focused guide based on the detailed change log. [MIGRATION GUIDE](https://github.com/cognitedata/cognite-sdk-python/blob/master/MIGRATION_GUIDE.md).

## Contributing
Want to contribute? Check out [CONTRIBUTING](https://github.com/cognitedata/cognite-sdk-python/blob/master/CONTRIBUTING.md).

