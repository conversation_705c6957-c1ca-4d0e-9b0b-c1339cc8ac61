import os
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)

from utils.general_utils import generate_query
from services.graphql_service import GraphqlService
from model.date_config import DateConfig
from datetime import datetime, timezone


class Stewardship:
    def __init__(self, graphql_service: GraphqlService):
        self.graphql_service = graphql_service

    def get_category_filter(self, kpi_id: str):
        match kpi_id:
            case "GKPI-SOL-RCI":
                filter = {"category": {"name": {"in": ["PPS-T1", "PPS-T2"]}}}
            case "GKPI-SOL-NFA":
                filter = {"category": {"name": {"in": ["PPS-T3"]}}}
            case "GKPI-SOL-PST":
                filter = {"category": {"name": {"in": ["PCS-T1", "PCS-T2"]}}}
            case "GKPI-SOL-EVT":
                filter = {"category": {"name": {"in": ["ENV-T1", "ENV-T2"]}}}
            case "GKPI-SOL-NFT":
                filter = {"category": {"name": {"in": ["FIR-T1", "FIR-T2"]}}}
            case "GKPI-SOL-NNM":
                filter = {
                    "category": {
                        "name": {
                            "in": [
                                "PPS-NM",
                                "PCS-NM",
                                "ENV-NM",
                                "FIR-NM",
                                "RLB-NM",
                                "SEC-NM",
                            ]
                        }
                    }
                }
            case "GKPI-SOL-HPE":
                filter = {"isHighPotential": {"eq": "true"}}

        return filter

    def group_asset(
        self, kpi_result: list
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        result_by_site = {}
        result_by_business_segment = {}
        for item in kpi_result:
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            reporting_unit = item.get("reportingUnit")
            business_segment = (
                reporting_unit.get("businessSegment", {}).get("externalId")
                if reporting_unit and reporting_unit.get("businessSegment")
                else None
            )
            if site:
                result_by_site.setdefault(site, 0)
                result_by_site[site] += 1
                if business_segment:
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] += 1

        return result_by_site, result_by_business_segment

    def group_near_misses(
        self, incidents: list, impacts: list
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        result_by_site = {}
        result_by_business_segment = {}
        incidents_name = []
        impacts_name = []
        for item in incidents:
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            reporting_unit = item.get("reportingUnit")
            business_segment = (
                reporting_unit.get("businessSegment", {}).get("externalId")
                if reporting_unit and reporting_unit.get("businessSegment")
                else None
            )
            inc_code = item['name'] + '_' + (reporting_unit['externalId'] if reporting_unit is not None else '') + '_' + item['enablonType']['externalId'] + '_' + item['incidentStartTime'][:7]
            if inc_code not in incidents_name:
                incidents_name.append(inc_code)
                if site and business_segment:
                    result_by_site.setdefault(site, 0)
                    result_by_site[site] += 1
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] += 1
        for item in impacts:
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            reporting_unit = item.get("reportingUnit")
            business_segment = (
                reporting_unit.get("businessSegment", {}).get("externalId")
                if reporting_unit and reporting_unit.get("businessSegment")
                else None
            )
            imp_code = item['name'] + '_' + (reporting_unit['externalId'] if reporting_unit is not None else '')+ '_' + item['category']['externalId'] + '_' + item['impactStartTime'][:7]
            if imp_code not in impacts_name:
                impacts_name.append(imp_code)
                if site and business_segment:
                    result_by_site.setdefault(site, 0)
                    result_by_site[site] += 1
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] += 1

        return result_by_site, result_by_business_segment

    def group_asset_trir(
        self,
        kpi_result: list,
        proprety,
        recordable: list,
        is_contractor: bool,
        is_total: bool,
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        hours_by_site = {}
        hours_by_business_segment = {}
        result_by_site = {}
        result_by_business_segment = {}

        for item in kpi_result:
            reporting_unit = item.get("reportingUnit")
            site = (
                reporting_unit.get("reportingSites", [{}])[0].get("externalId")
                if reporting_unit and reporting_unit.get("reportingSites")
                else None
            )
            business_segment = (
                reporting_unit.get("businessSegment", {}).get("externalId")
                if reporting_unit and reporting_unit.get("businessSegment")
                else None
            )

            if site:
                hours_by_site.setdefault(site, 0)
                hours_by_site[site] += (
                    float(item[proprety]) if item[proprety] is not None else 0
                )
                if business_segment:
                    hours_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    hours_by_business_segment[site][business_segment] += (
                        float(item[proprety]) if item[proprety] is not None else 0
                    )

        recordable_result_by_site = {}
        recordable_result_by_business_segment = {}
        for item in recordable:
            site = item.get("reportingSite", {}).get("externalId")
            reporting_unit = item.get("reportingUnit")
            business_segment = (
                reporting_unit.get("businessSegment", {}).get("externalId")
                if reporting_unit
                else None
            )
            if site and (is_total or item["isContractor"] == is_contractor):
                recordable_result_by_site.setdefault(site, 0)
                recordable_result_by_site[site] += 1
                if business_segment:
                    recordable_result_by_business_segment.setdefault(
                        site, {}
                    ).setdefault(business_segment, 0)
                    recordable_result_by_business_segment[site][business_segment] += 1

        for site in hours_by_site.keys():
            result_by_site[site] = (
                (200000 * recordable_result_by_site[site]) / hours_by_site[site]
                if hours_by_site[site] != 0 and site in recordable_result_by_site
                else 0.0
            )

        for site in hours_by_business_segment.keys():
            for business_segment in hours_by_business_segment[site].keys():
                result_by_business_segment.setdefault(site, {}).setdefault(
                    business_segment, 0.0
                )
                result_by_business_segment[site][business_segment] = (
                    (
                        200000
                        * recordable_result_by_business_segment[site][business_segment]
                    )
                    / hours_by_business_segment[site][business_segment]
                    if hours_by_business_segment[site][business_segment] != 0
                    and site in recordable_result_by_business_segment
                    and business_segment in recordable_result_by_business_segment[site]
                    else 0.0
                )

        return result_by_site, result_by_business_segment

    def get_unit_filter(self, sites):
        units_grn = []
        for site in sites:
            units_grn.append("UNT-" + site["siteCode"] + "GRN")

        units_filter = {"not": {"reportingUnit": {"externalId": {"in": units_grn}}}}
        return units_filter

    def get_sites_filter(self, sites, site_proprety):
        sites_ids = []
        for site in sites:
            sites_ids.append(site["externalId"])
        return {site_proprety: {"externalId": {"in": sites_ids}}}

    async def get_stewardship_kpi_by_name(
        self, kpi_id: str, now, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        date_config = DateConfig().get_config(now)
        category_filter = self.get_category_filter(kpi_id=kpi_id)
        stewardship_site_result = {}
        stewardship_business_segment_result = {}

        units_filter = self.get_unit_filter(sites)
        site_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            stewardship_filter = {
                "and": [
                    {
                        "date": {
                            "gte": date_config[date]["start"][:10],
                            "lte": date_config[date]["end"][:10],
                        }
                    },
                    category_filter,
                    units_filter,
                    site_filter,
                ]
            }

            stewardship_selection = """
            externalId
            reportingSite {
                externalId
            }
            reportingUnit {
                externalId
                businessSegment {
                    externalId
                }
            }
            """

            stewardship_kpi = await self.graphql_service.get_all_results_list(
                generate_query("listSwpIncidentImpact", stewardship_selection),
                "listSwpIncidentImpact",
                stewardship_filter,
            )

            print("{} - {}: {} results".format(kpi_id, date, len(stewardship_kpi)))
            stewardship_site_result[date], stewardship_business_segment_result[date] = (
                self.group_asset(stewardship_kpi)
            )

        return stewardship_site_result, stewardship_business_segment_result

    async def get_trir_kpi(
        self, now, recordables, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        date_config = DateConfig().get_config(now)
        trir_total_by_site = {}
        trir_employee_by_site = {}
        trir_contractor_by_site = {}

        trir_total_by_business_segment = {}
        trir_employee_by_business_segment = {}
        trir_contractor_by_business_segment = {}

        units_filter = self.get_unit_filter(sites)
        site_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            firstday = date_config[date]["start"][:4] + "-01-01"

            trir_filter = {
                "and": [
                    {
                        "firstDayOfMonth": {
                            "gte": firstday,
                            "lte": date_config[date]["end"][:10],
                        }
                    },
                    units_filter,
                    site_filter,
                ]
            }

            trir_selection = """
            externalId
            employeeWorkingHours
            contractorWorkingHours
            totalWorkingHours
            reportingUnit {
                externalId
                reportingSites {
                    items {
                        externalId
                    }
                }
                businessSegment {
                    externalId
                }
            }
            """

            trir_kpi = await self.graphql_service.get_all_results_list(
                generate_query("listWorkingHourReport", trir_selection),
                "listWorkingHourReport",
                trir_filter,
            )

            print("TRIR - {}: {} results".format(date, len(trir_kpi)))

            trir_total_by_site[date], trir_total_by_business_segment[date] = (
                self.group_asset_trir(
                    trir_kpi,
                    "totalWorkingHours",
                    recordables[date],
                    False,
                    True,
                )
            )
            trir_employee_by_site[date], trir_employee_by_business_segment[date] = (
                self.group_asset_trir(
                    trir_kpi,
                    "employeeWorkingHours",
                    recordables[date],
                    False,
                    False,
                )
            )
            trir_contractor_by_site[date], trir_contractor_by_business_segment[date] = (
                self.group_asset_trir(
                    trir_kpi,
                    "contractorWorkingHours",
                    recordables[date],
                    True,
                    False,
                )
            )

        trir_result_by_site = {
            "GKPI-SOL-TTL": trir_total_by_site,
            "GKPI-SOL-TEP": trir_employee_by_site,
            "GKPI-SOL-TCT": trir_contractor_by_site,
        }
        trir_result_by_business_segment = {
            "GKPI-SOL-TTL": trir_total_by_business_segment,
            "GKPI-SOL-TEP": trir_employee_by_business_segment,
            "GKPI-SOL-TCT": trir_contractor_by_business_segment,
        }

        return trir_result_by_site, trir_result_by_business_segment

    async def get_stewardship_kpi(
        self, now, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        global_kpis_by_site = {}
        global_kpis_by_business_segment = {}
        kpis = [
            "GKPI-SOL-RCI",
            "GKPI-SOL-NFA",
            "GKPI-SOL-PST",
            "GKPI-SOL-EVT",
            "GKPI-SOL-NFT",
            "GKPI-SOL-HPE",
        ]

        for kpi in kpis:
            (
                kpi_site_result,
                kpi_business_segment_result,
            ) = await self.get_stewardship_kpi_by_name(kpi_id=kpi, now=now, sites=sites)
            global_kpis_by_site[kpi] = kpi_site_result
            global_kpis_by_business_segment[kpi] = kpi_business_segment_result

        return global_kpis_by_site, global_kpis_by_business_segment

    async def get_near_misses(self, kpi_id, now, sites):
        date_config = DateConfig().get_config(now)
        category_filter = self.get_category_filter(kpi_id=kpi_id)
        near_misses_site_result = {}
        near_misses_business_segment_result = {}

        units_filter = self.get_unit_filter(sites)
        site_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            incidents_filter = {
                "and": [
                    {
                        "incidentStartTime": {
                            "gte": date_config[date]["start"],
                            "lte": date_config[date]["end"],
                        }
                    },
                    {"enablonType": {"externalId": {"eq": "EIT-NEM"}}},
                    {"dataSource": {"eq": "ENA"}},
                    units_filter,
                    site_filter,
                ]
            }

            impacts_filter = {
                "and": [
                    {
                        "impactStartTime": {
                            "gte": date_config[date]["start"],
                            "lte": date_config[date]["end"],
                        }
                    },
                    {"not": {"externalId": {"prefix": "ENA"}}},
                    category_filter,
                    units_filter,
                    site_filter,
                ]
            }

            near_misses_selection_inc = """
            externalId
            name
            incidentStartTime
            enablonType{
                externalId
                }
            reportingSite {
                externalId
            }
            reportingUnit {
                externalId
                businessSegment {
                    externalId
                }
            }
            """

            near_misses_selection_imp = """
            externalId
            name
            impactStartTime
            category{
                externalId
            }
            reportingSite {
                externalId
            }
            reportingUnit {
                externalId
                businessSegment {
                    externalId
                }
            }
            """

            incidents = await self.graphql_service.get_all_results_list(
                generate_query("listIncident", near_misses_selection_inc),
                "listIncident",
                incidents_filter,
            )

            impacts = await self.graphql_service.get_all_results_list(
                generate_query("listIncidentImpact", near_misses_selection_imp),
                "listIncidentImpact",
                impacts_filter,
            )

            print(
                "{} - {}: {} results".format(
                    kpi_id, date, len(incidents) + len(impacts)
                )
            )
            near_misses_site_result[date], near_misses_business_segment_result[date] = (
                self.group_near_misses(incidents, impacts)
            )

        return near_misses_site_result, near_misses_business_segment_result

    async def get_near_misses_kpi(
        self, now, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        global_kpis_by_site = {}
        global_kpis_by_business_segment = {}

        kpi_id = "GKPI-SOL-NNM"

        kpi_site_result, kpi_business_segment_result = await self.get_near_misses(
            kpi_id=kpi_id, now=now, sites=sites
        )
        global_kpis_by_site[kpi_id] = kpi_site_result
        global_kpis_by_business_segment[kpi_id] = kpi_business_segment_result

        return global_kpis_by_site, global_kpis_by_business_segment

    async def get_stewardship_trir(
        self, now, recordables, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        global_kpis_by_site = {}
        global_kpis_by_business_segment = {}
        trirs = ["GKPI-SOL-TTL", "GKPI-SOL-TEP", "GKPI-SOL-TCT"]
        trir_result_by_site, trir_result_by_business_segment = await self.get_trir_kpi(
            now=now, recordables=recordables, sites=sites
        )
        for trir in trirs:
            global_kpis_by_site[trir] = trir_result_by_site[trir]
            global_kpis_by_business_segment[trir] = trir_result_by_business_segment[
                trir
            ]

        return global_kpis_by_site, global_kpis_by_business_segment

    async def get_recordables_list(self, now, sites):
        date_config = DateConfig().get_config(now)
        category_filter = self.get_category_filter(kpi_id="GKPI-SOL-RCI")
        recordable_result = {}

        units_filter = self.get_unit_filter(sites)
        site_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            today = datetime.now(timezone.utc).isoformat()
            if today <= date_config[date]["start"]:
                recordable_result[date] = []
            else:
                firstday = (
                    datetime(year=int(date_config[date]["start"][:4]), month=1, day=1)
                    .replace(microsecond=0)
                    .isoformat()
                )
                recordable_filter = {
                    "and": [
                        {
                            "reportedTime": {
                                "gte": firstday,
                                "lte": date_config[date]["end"],
                            }
                        },
                        category_filter,
                        units_filter,
                        site_filter,
                    ]
                }

                recordable_selection = """
                externalId
                isContractor
                reportingSite {
                    externalId
                }
                reportingUnit {
                    externalId
                    businessSegment {
                        externalId
                    }
                }
                """

                recordable_result[
                    date
                ] = await self.graphql_service.get_all_results_list(
                    generate_query("listSwpIncidentImpact", recordable_selection),
                    "listSwpIncidentImpact",
                    recordable_filter,
                )

        return recordable_result
