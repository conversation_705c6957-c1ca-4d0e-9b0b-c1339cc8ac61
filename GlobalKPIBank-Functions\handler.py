from datetime import datetime
import asyncio
import csv
import os

from update_stewardship.run import run_update_stewardship
from update_foundational.run import run_update_foundational
from update_foundational_productivity_prd_first_half.run import (
    run_update_foundational_productivity_prd_first_half,
)
from update_foundational_productivity_prd_second_half.run import (
    run_update_foundational_productivity_prd_second_half,
)
from update_foundational_productivity_pny.run import (
    run_update_foundational_productivity_pny,
)
from update_foundational_productivity_ppn.run import (
    run_update_foundational_productivity_ppn,
)
from update_quality.run import run_update_quality
from update_block_stock.run import run_update_block_stock
from update_quality_first_pass_yield_first_half.run import (
    run_update_quality_first_pass_yield_first_half,
)
from update_quality_first_pass_yield_second_half.run import (
    run_update_quality_first_pass_yield_second_half,
)
from update_targets.run import run_update_targets

from infra.cognite_client_factory import CogniteClientFactory
from infra.graphql_client_factory import GraphqlClientFactory
from infra.env_variables import EnvVariables
from infra.env_variables_qa import EnvVariablesQA
from infra.env_variables_prod import EnvVariablesPROD

from services.graphql_service import GraphqlService
from services.reporting_service import ReportingService
from services.kpis.stewardship import Stewardship
from services.kpis.quality import Quality
from services.kpis.foundational import Foundational
from services.kpis.global_kpi_service import GlobalKPIService


def handle(data=None, secrets=None, function_call_info=None):
    """Calculate all the boiler functions. The function ran depends on
    the data input

    Args:
        data (Dict, optional):
        data = {"start_date": timestamp,
            "end_date": timestamp,
            "executable-name": "total_efficiency",
            "environment":"dev"
            "backfill": "yes"}
            where "executable-name" may be:
                - "update_foundational"
                - "update_foundational_productivity_pny"
                - "update_foundational_productivity_ppn"
                - "update_foundational_productivity_prd_first_half"
                - "update_foundational_productivity_prd_second_half"
                - "update_quality"
                - "update_quality_first_pass_yield_first_half"
                - "update_quality_first_pass_yield_second_half"
                - "update_stewardship"
            where "environment" may be:
                - "dev"
                - "qa"
                - "prod"
            all keys are optional except for executable name and environment
        Defaults to None.
        secrets (Dict, optional): Defaults to None.
        function_call_info (_type_, optional): Defaults to None.
    """

    # detect function to be run and set path
    try:
        executable_name = str(data["executable-name"]).casefold()
        environment = str(data["environment"]).casefold()
    except Exception as e:
        print("Could not read executable name or environment.")
        return

    base_path = os.path.dirname(os.path.abspath(__file__))
    function_path = os.path.join(base_path, str(f"{executable_name}"))
    if not os.path.isdir(function_path):
        print("No function matching name")

    # get environment and create client
    try:
        if environment == "dev":
            env_variables = EnvVariables()
        elif environment == "qa":
            env_variables = EnvVariablesQA()
        elif environment == "prod":
            env_variables = EnvVariablesPROD()
        cognite_client = CogniteClientFactory.create(env_variables)
    except Exception as e:
        print("No environment matching name. ", e)
        return

    # send data and client to run
    executable_class = globals()[f"run_{executable_name}"]

    execute = executable_class(cognite_client, data, env_variables)
    asyncio.run(execute.run())
    return


if __name__ == "__main__":
    env = "dev"
    data = {"executable-name": "update_stewardship", "environment": env}
    print("Running stewardship", data)
    handle(data)
    # data = {"executable-name": "update_block_stock", "environment": env}
    # handle(data)
    # data = {"executable-name": "update_quality", "environment": env}
    # handle(data)
    # data = {
    #     "executable-name": "update_quality_first_pass_yield_first_half",
    #     "environment": env,
    # }
    # handle(data)
    # data = {
    #     "executable-name": "update_quality_first_pass_yield_second_half",
    #     "environment": env,
    # }
    # handle(data)
    # data = {"executable-name": "update_foundational", "environment": env}
    # handle(data)
    # data = {
    #     "executable-name": "update_foundational_productivity_pny",
    #     "environment": env,
    # }
    # handle(data)
    # data = {
    #     "executable-name": "update_foundational_productivity_ppn",
    #     "environment": env,
    # }
    # handle(data)
    # data = {
    #     "executable-name": "update_foundational_productivity_prd_first_half",
    #     "environment": env,
    # }
    # handle(data)
    # data = {
    #     "executable-name": "update_foundational_productivity_prd_second_half",
    #     "environment": env,
    # }
    # handle(data)
