from datetime import datetime
import time
import asyncio
from cognite.client import CogniteClient
from zoneinfo import ZoneInfo
import traceback
import os
import sys
import glob
import math
from typing import List, Dict, Tuple

sys.path.append(
    os.path.abspath(
        os.path.join(
            os.path.dirname(__file__),
            "..",
        )
    )
)

from infra.graphql_client_factory import GraphqlClientFactory
from infra.env_variables_prod import EnvVariablesPROD
from infra.env_variables import EnvVariables
from infra.env_variables_qa import EnvVariablesQA
from infra.cognite_client_factory import CogniteClientFactory

from services.graphql_service import GraphqlService
from services.reporting_service import ReportingService
from services.business_segment_service import BusinessSegmentService
from services.kpis.foundational import Foundational
from services.kpis.global_kpi_service import GlobalKPIService

from utils.env_variable import EnvVariable

base_path = os.path.dirname(os.path.abspath(__file__))


class NoOperationPeriodException(Exception):
    pass


class StackO2TooHighException(Exception):
    pass


class run_update_foundational_productivity_pny:
    def __init__(
        self, cognite_client: CogniteClient, data: dict, env_variables: EnvVariable
    ):
        self.cognite_client = cognite_client
        self.data = data
        self.env_variables = env_variables

    async def run(self):
        print("Code version 2025-04-03")
        print("Task started...")

        env_variables_prod = EnvVariablesQA()
        cognite_client_prod = CogniteClientFactory.create(env_variables_prod)

        # get reporting sites mfg
        graphql_client_asset = GraphqlClientFactory.create(
            cognite_client_prod, env_variables_prod.cognite, "asset"
        )
        graphql_service = GraphqlService(graphql_client_asset)
        reporting_unit_service = ReportingService(graphql_service)
        reporting_sites = await reporting_unit_service.get_reporting_sites()
        business_segments_service = BusinessSegmentService(graphql_service)
        business_segments = await business_segments_service.get_business_segments()
        await graphql_service.cleanup()

        now = datetime.now()
        kpis_by_site = {}
        kpis_by_business_segment = {}

        print("Updating foundational productivity PNY kpi...")
        graphql_client_executive = GraphqlClientFactory.create(
            cognite_client_prod, env_variables_prod.cognite, "executive"
        )
        graphql_service = GraphqlService(graphql_client_executive)
        foundational_service = Foundational(graphql_service)
        (
            pny_kpis_by_site,
            pny_kpis_by_business_segment,
        ) = await foundational_service.get_foundational_productivity_kpi(
            now=now, sites=reporting_sites, kpi="GKPI-SOL-PNY"
        )
        kpis_by_site.update(pny_kpis_by_site)
        kpis_by_business_segment.update(pny_kpis_by_business_segment)
        await graphql_service.cleanup()

        # saving global kpis
        print("Saving global kpis...")
        global_kpi_service = GlobalKPIService(
            self.cognite_client, self.env_variables.cognite
        )
        await global_kpi_service.update_global_kpis(
            kpis_by_site, kpis_by_business_segment, reporting_sites, business_segments
        )
        print("Global kpis saved!")

        return
