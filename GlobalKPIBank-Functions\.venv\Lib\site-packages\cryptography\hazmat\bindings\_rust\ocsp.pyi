# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

import datetime
from collections.abc import Iterator

from cryptography import x509
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric.types import PrivateKeyTypes
from cryptography.x509 import ocsp

class OCSPRequest:
    @property
    def issuer_key_hash(self) -> bytes: ...
    @property
    def issuer_name_hash(self) -> bytes: ...
    @property
    def hash_algorithm(self) -> hashes.HashAlgorithm: ...
    @property
    def serial_number(self) -> int: ...
    def public_bytes(self, encoding: serialization.Encoding) -> bytes: ...
    @property
    def extensions(self) -> x509.Extensions: ...

class OCSPResponse:
    @property
    def responses(self) -> Iterator[OCSPSingleResponse]: ...
    @property
    def response_status(self) -> ocsp.OCSPResponseStatus: ...
    @property
    def signature_algorithm_oid(self) -> x509.ObjectIdentifier: ...
    @property
    def signature_hash_algorithm(
        self,
    ) -> hashes.HashAlgorithm | None: ...
    @property
    def signature(self) -> bytes: ...
    @property
    def tbs_response_bytes(self) -> bytes: ...
    @property
    def certificates(self) -> list[x509.Certificate]: ...
    @property
    def responder_key_hash(self) -> bytes | None: ...
    @property
    def responder_name(self) -> x509.Name | None: ...
    @property
    def produced_at(self) -> datetime.datetime: ...
    @property
    def produced_at_utc(self) -> datetime.datetime: ...
    @property
    def certificate_status(self) -> ocsp.OCSPCertStatus: ...
    @property
    def revocation_time(self) -> datetime.datetime | None: ...
    @property
    def revocation_time_utc(self) -> datetime.datetime | None: ...
    @property
    def revocation_reason(self) -> x509.ReasonFlags | None: ...
    @property
    def this_update(self) -> datetime.datetime: ...
    @property
    def this_update_utc(self) -> datetime.datetime: ...
    @property
    def next_update(self) -> datetime.datetime | None: ...
    @property
    def next_update_utc(self) -> datetime.datetime | None: ...
    @property
    def issuer_key_hash(self) -> bytes: ...
    @property
    def issuer_name_hash(self) -> bytes: ...
    @property
    def hash_algorithm(self) -> hashes.HashAlgorithm: ...
    @property
    def serial_number(self) -> int: ...
    @property
    def extensions(self) -> x509.Extensions: ...
    @property
    def single_extensions(self) -> x509.Extensions: ...
    def public_bytes(self, encoding: serialization.Encoding) -> bytes: ...

class OCSPSingleResponse:
    @property
    def certificate_status(self) -> ocsp.OCSPCertStatus: ...
    @property
    def revocation_time(self) -> datetime.datetime | None: ...
    @property
    def revocation_time_utc(self) -> datetime.datetime | None: ...
    @property
    def revocation_reason(self) -> x509.ReasonFlags | None: ...
    @property
    def this_update(self) -> datetime.datetime: ...
    @property
    def this_update_utc(self) -> datetime.datetime: ...
    @property
    def next_update(self) -> datetime.datetime | None: ...
    @property
    def next_update_utc(self) -> datetime.datetime | None: ...
    @property
    def issuer_key_hash(self) -> bytes: ...
    @property
    def issuer_name_hash(self) -> bytes: ...
    @property
    def hash_algorithm(self) -> hashes.HashAlgorithm: ...
    @property
    def serial_number(self) -> int: ...

def load_der_ocsp_request(data: bytes) -> ocsp.OCSPRequest: ...
def load_der_ocsp_response(data: bytes) -> ocsp.OCSPResponse: ...
def create_ocsp_request(
    builder: ocsp.OCSPRequestBuilder,
) -> ocsp.OCSPRequest: ...
def create_ocsp_response(
    status: ocsp.OCSPResponseStatus,
    builder: ocsp.OCSPResponseBuilder | None,
    private_key: PrivateKeyTypes | None,
    hash_algorithm: hashes.HashAlgorithm | None,
) -> ocsp.OCSPResponse: ...
