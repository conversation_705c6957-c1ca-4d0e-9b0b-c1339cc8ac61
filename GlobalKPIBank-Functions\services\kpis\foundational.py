import os
import sys
from datetime import datetime
from dateutil.relativedelta import relativedelta

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)

from utils.general_utils import generate_query
from utils.enums import Productivity
from services.graphql_service import GraphqlService
from model.date_config import DateConfig


class Foundational:
    def __init__(self, graphql_service: GraphqlService):
        self.graphql_service = graphql_service

    def get_sites_filter(self, sites, site_proprety):
        sites_ids = []
        for site in sites:
            sites_ids.append(site["externalId"])
        return {site_proprety: {"externalId": {"in": sites_ids}}}

    def group_unit_count(self, kpi_result: list) -> dict[str, list[dict]]:
        result = {}
        for item in kpi_result:
            if item["reportingUnit"] is not None:
                unit = item["reportingUnit"]["externalId"]
                if unit in result:
                    result[unit] += 1
                else:
                    result[unit] = 1
        return result

    def group_unit_sum(
        self, kpi_result: list, proprety, site_proprety, business_segment_property
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        result_by_site = {}
        result_by_business_segment = {}
        for item in kpi_result:
            business_segment = (
                item.get(business_segment_property).get("externalId")
                if item.get(business_segment_property)
                else None
            )
            site = (
                item.get(site_proprety).get("externalId")
                if item.get(site_proprety)
                else None
            )
            if site:
                result_by_site.setdefault(site, 0)
                result_by_site[site] += (
                    float(item[proprety]) if item[proprety] is not None else 0
                )
                if business_segment:
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] += (
                        float(item[proprety]) if item[proprety] is not None else 0
                    )

        return result_by_site, result_by_business_segment

    def group_unit_diff(
        self, actual: list, forecast: list, proprety
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        result_by_site = {}
        result_by_business_segment = {}
        for item in actual:
            business_segment = (
                item.get("businessSegment").get("externalId")
                if item.get("businessSegment")
                else None
            )
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            if site:
                result_by_site.setdefault(site, 0)
                result_by_site[site] += (
                    item[proprety] if item[proprety] is not None else 0
                )
                if business_segment:
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] += (
                        item[proprety] if item[proprety] is not None else 0
                    )

        for item in forecast:
            business_segment = (
                item.get("businessSegment").get("externalId")
                if item.get("businessSegment")
                else None
            )
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            if site:
                result_by_site.setdefault(site, 0)
                result_by_site[site] -= (
                    item[proprety] if item[proprety] is not None else 0
                )
                if business_segment:
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] -= (
                        item[proprety] if item[proprety] is not None else 0
                    )

        return result_by_site, result_by_business_segment

    def get_sum_cost_variance(
        self, kpi_list: list, property, energy_cost
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        result_by_site = {}
        result_by_business_segment = {}
        for item in kpi_list:
            business_segment = (
                item.get("businessSegment").get("externalId")
                if item.get("businessSegment")
                else None
            )
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            if site:
                value = (item[property] if item[property] is not None else 0) + (
                    item[energy_cost] if item[energy_cost] is not None else 0
                )

                result_by_site.setdefault(site, 0)
                result_by_site[site] += value
                if business_segment:
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] += value

        return result_by_site, result_by_business_segment

    def get_diff_cost_variance(
        self, actual: list, forecast: list, property, energy_cost
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        result_by_site = {}
        result_by_business_segment = {}
        for item in actual:
            business_segment = (
                item.get("businessSegment").get("externalId")
                if item.get("businessSegment")
                else None
            )
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            if site:
                value = (item[property] if item[property] is not None else 0) + (
                    item[energy_cost] if item[energy_cost] is not None else 0
                )

                result_by_site.setdefault(site, 0)
                result_by_site[site] += value
                if business_segment:
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] += value

        for item in forecast:
            business_segment = (
                item.get("businessSegment").get("externalId")
                if item.get("businessSegment")
                else None
            )
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            if site:
                value = (item[property] if item[property] is not None else 0) + (
                    item[energy_cost] if item[energy_cost] is not None else 0
                )

                result_by_site.setdefault(site, 0)
                result_by_site[site] -= value
                if business_segment:
                    result_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_by_business_segment[site][business_segment] -= value

        return result_by_site, result_by_business_segment

    def group_unit_div(
        self, kg: list, headcount: list, proprety_kg, proprety_headcount
    ) -> dict[str, list[dict]]:
        numerator = {}
        denominator = {}
        result = {}

        for item in kg:
            if item["reportingSite"] is not None:
                site = item["reportingSite"]["externalId"]
                if site in numerator:
                    numerator[site] += (
                        item[proprety_kg] if item[proprety_kg] is not None else 0
                    )
                else:
                    numerator[site] = (
                        item[proprety_kg] if item[proprety_kg] is not None else 0
                    )

        for item in headcount:
            if item["refSite"] is not None:
                site = item["refSite"]["externalId"]
                if site in denominator:
                    denominator[site] += (
                        item[proprety_headcount]
                        if item[proprety_headcount] is not None
                        else 0
                    )
                else:
                    denominator[site] = (
                        item[proprety_headcount]
                        if item[proprety_headcount] is not None
                        else 0
                    )

        for key in numerator.keys():
            if key not in result:
                if (
                    (key in numerator)
                    and (key in denominator)
                    and (denominator[key] != 0)
                ):
                    result[key] = numerator[key] / denominator[key]
                else:
                    result[key] = 0
        return result

    def group_unit_kg_actual_forecast(
        self, kpis: list
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        result_by_site = {}
        result_by_business_segment = {}
        actual_cost_by_site = {}
        actual_volume_by_site = {}
        forecast_cost_by_site = {}
        forecast_volume_by_site = {}

        actual_cost_by_business_segment = {}
        actual_volume_by_business_segment = {}
        forecast_cost_by_business_segment = {}
        forecast_volume_by_business_segment = {}

        for item in kpis:
            business_segment = (
                item.get("businessSegment").get("externalId")
                if item.get("businessSegment")
                else None
            )
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            if site:
                actual_cost_by_site.setdefault(site, 0)
                forecast_cost_by_site.setdefault(site, 0)
                actual_volume_by_site.setdefault(site, 0)
                forecast_volume_by_site.setdefault(site, 0)

                actual_cost_by_site[site] += (
                    item["actualPlantPeriodCost"]
                    if item["actualPlantPeriodCost"] is not None
                    else 0
                )
                actual_cost_by_site[site] += (
                    item["actualEnergyCost"]
                    if item["actualEnergyCost"] is not None
                    else 0
                )
                actual_volume_by_site[site] += (
                    item["actualProductionVolume"]
                    if item["actualProductionVolume"] is not None
                    else 0
                )

                forecast_cost_by_site[site] += (
                    item["forecastPlantPeriodCost"]
                    if item["forecastPlantPeriodCost"] is not None
                    else 0
                )
                forecast_cost_by_site[site] += (
                    item["forecastEnergyCost"]
                    if item["forecastEnergyCost"] is not None
                    else 0
                )
                forecast_volume_by_site[site] += (
                    item["forecastProductionVolume"]
                    if item["forecastProductionVolume"] is not None
                    else 0
                )
                if business_segment:
                    actual_cost_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    forecast_cost_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    actual_volume_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    forecast_volume_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )

                    actual_cost_by_business_segment[site][business_segment] += (
                        item["actualPlantPeriodCost"]
                        if item["actualPlantPeriodCost"] is not None
                        else 0
                    )
                    actual_cost_by_business_segment[site][business_segment] += (
                        item["actualEnergyCost"]
                        if item["actualEnergyCost"] is not None
                        else 0
                    )
                    actual_volume_by_business_segment[site][business_segment] += (
                        item["actualProductionVolume"]
                        if item["actualProductionVolume"] is not None
                        else 0
                    )

                    forecast_cost_by_business_segment[site][business_segment] += (
                        item["forecastPlantPeriodCost"]
                        if item["forecastPlantPeriodCost"] is not None
                        else 0
                    )
                    forecast_cost_by_business_segment[site][business_segment] += (
                        item["forecastEnergyCost"]
                        if item["forecastEnergyCost"] is not None
                        else 0
                    )
                    forecast_volume_by_business_segment[site][business_segment] += (
                        item["forecastProductionVolume"]
                        if item["forecastProductionVolume"] is not None
                        else 0
                    )

        for site in actual_cost_by_site.keys():
            result_actual = (
                actual_cost_by_site[site] / actual_volume_by_site[site]
                if actual_volume_by_site[site] != 0
                else 0.0
            )
            result_forecast = (
                forecast_cost_by_site[site] / forecast_volume_by_site[site]
                if forecast_volume_by_site[site] != 0
                else 0.0
            )
            result_by_site[site] = result_actual - result_forecast
            for business_segment in actual_cost_by_business_segment[site]:
                result_by_business_segment.setdefault(site, {}).setdefault(
                    business_segment, 0
                )

                result_actual = (
                    actual_cost_by_business_segment[site][business_segment]
                    / actual_volume_by_business_segment[site][business_segment]
                    if actual_volume_by_business_segment[site][business_segment] != 0
                    else 0.0
                )
                result_forecast = (
                    forecast_cost_by_business_segment[site][business_segment]
                    / forecast_volume_by_business_segment[site][business_segment]
                    if forecast_volume_by_business_segment[site][business_segment] != 0
                    else 0.0
                )
                result_by_business_segment[site][business_segment] = (
                    result_actual - result_forecast
                )

        return result_by_site, result_by_business_segment

    def group_unit_cash_margin(
        self, kpis: list
    ) -> tuple[
        dict[str, list[dict]],
        dict[str, list[dict]],
        dict[str, list[dict]],
        dict[str, list[dict]],
    ]:
        result_cash_margin_by_site = {}
        result_cash_margin_by_business_segment = {}

        result_kg_cash_margin_by_site = {}
        result_kg_cash_margin_by_business_segment = {}

        variable_margin_by_site = {}
        variable_margin_by_business_segment = {}

        fixed_distribution_cost_by_site = {}
        fixed_distribution_cost_by_business_segment = {}

        other_fixed_cost_by_site = {}
        other_fixed_cost_by_business_segment = {}

        plant_period_cost_by_site = {}
        plant_period_cost_by_business_segment = {}

        energy_cost_by_site = {}
        energy_cost_by_business_segment = {}

        sales_volume_by_site = {}
        sales_volume_by_business_segment = {}

        for item in kpis:
            business_segment = (
                item.get("businessSegment").get("externalId")
                if item.get("businessSegment")
                else None
            )
            site = (
                item.get("reportingSite").get("externalId")
                if item.get("reportingSite")
                else None
            )
            if site:
                variable_margin_by_site.setdefault(site, 0)
                fixed_distribution_cost_by_site.setdefault(site, 0)
                other_fixed_cost_by_site.setdefault(site, 0)
                plant_period_cost_by_site.setdefault(site, 0)
                energy_cost_by_site.setdefault(site, 0)
                sales_volume_by_site.setdefault(site, 0)

                variable_margin_by_site[site] += (
                    item["variableMargin"] if item["variableMargin"] is not None else 0
                )
                fixed_distribution_cost_by_site[site] += (
                    item["fixedDistributionCost"]
                    if item["fixedDistributionCost"] is not None
                    else 0
                )
                other_fixed_cost_by_site[site] += (
                    item["otherFixedCost"] if item["otherFixedCost"] is not None else 0
                )
                plant_period_cost_by_site[site] += (
                    item["plantPeriodCost"]
                    if item["plantPeriodCost"] is not None
                    else 0
                )
                energy_cost_by_site[site] += (
                    item["energyCost"] if item["energyCost"] is not None else 0
                )
                sales_volume_by_site[site] += (
                    item["salesVolume"] if item["salesVolume"] is not None else 0
                )
                if business_segment:
                    variable_margin_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    fixed_distribution_cost_by_business_segment.setdefault(
                        site, {}
                    ).setdefault(business_segment, 0)
                    other_fixed_cost_by_business_segment.setdefault(
                        site, {}
                    ).setdefault(business_segment, 0)
                    plant_period_cost_by_business_segment.setdefault(
                        site, {}
                    ).setdefault(business_segment, 0)
                    energy_cost_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    sales_volume_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )

                    variable_margin_by_business_segment[site][business_segment] += (
                        item["variableMargin"]
                        if item["variableMargin"] is not None
                        else 0
                    )
                    fixed_distribution_cost_by_business_segment[site][
                        business_segment
                    ] += (
                        item["fixedDistributionCost"]
                        if item["fixedDistributionCost"] is not None
                        else 0
                    )
                    other_fixed_cost_by_business_segment[site][business_segment] += (
                        item["otherFixedCost"]
                        if item["otherFixedCost"] is not None
                        else 0
                    )
                    plant_period_cost_by_business_segment[site][business_segment] += (
                        item["plantPeriodCost"]
                        if item["plantPeriodCost"] is not None
                        else 0
                    )
                    energy_cost_by_business_segment[site][business_segment] += (
                        item["energyCost"] if item["energyCost"] is not None else 0
                    )
                    sales_volume_by_business_segment[site][business_segment] += (
                        item["salesVolume"] if item["salesVolume"] is not None else 0
                    )

        for site in variable_margin_by_site.keys():
            result_cash_margin_by_site[site] = (
                variable_margin_by_site[site]
                - fixed_distribution_cost_by_site[site]
                - other_fixed_cost_by_site[site]
                - plant_period_cost_by_site[site]
                - energy_cost_by_site[site]
            )
            result_kg_cash_margin_by_site[site] = (
                result_cash_margin_by_site[site] / sales_volume_by_site[site]
                if sales_volume_by_site[site] != 0
                else 0.0
            )
            try:
                for business_segment in variable_margin_by_business_segment[site]:
                    result_cash_margin_by_business_segment.setdefault(site, {}).setdefault(
                        business_segment, 0
                    )
                    result_kg_cash_margin_by_business_segment.setdefault(
                        site, {}
                    ).setdefault(business_segment, 0)

                    result_cash_margin_by_business_segment[site][business_segment] = (
                        variable_margin_by_business_segment[site][business_segment]
                        - fixed_distribution_cost_by_business_segment[site][
                            business_segment
                        ]
                        - other_fixed_cost_by_business_segment[site][business_segment]
                        - plant_period_cost_by_business_segment[site][business_segment]
                        - energy_cost_by_business_segment[site][business_segment]
                    )
                    result_kg_cash_margin_by_business_segment[site][business_segment] = (
                        result_cash_margin_by_business_segment[site][business_segment]
                        / sales_volume_by_business_segment[site][business_segment]
                        if sales_volume_by_business_segment[site][business_segment] != 0
                        else 0.0
                    )
            except Exception as e:
                print("**************", e)
                raise e

        return (
            result_cash_margin_by_site,
            result_cash_margin_by_business_segment,
            result_kg_cash_margin_by_site,
            result_kg_cash_margin_by_business_segment,
        )

    def get_productivity_time_filter(self, kpi_id: str, date: str):
        match kpi_id:
            case "GKPI-SOL-PRD":
                filter = {"year": {"eq": date}}
            case "GKPI-SOL-PNY":
                filter = {"year": {"eq": str(int(date) + 1)}}
            case "GKPI-SOL-PPN":
                filter = {"year": {"eq": str(int(date) + 2)}}

        return filter

    def get_range_time_filter(self, start: str, end: str):
        start_date = datetime.strptime(start[:10], "%Y-%m-%d").date()
        end_date = datetime.strptime(end[:10], "%Y-%m-%d").date()

        filter = []

        while start_date < end_date:
            filter.append(
                {
                    "and": [
                        {"month": {"eq": start_date.strftime("%b").upper()}},
                        {"year": {"eq": start_date.year}},
                    ]
                }
            )
            start_date = start_date + relativedelta(months=+1)

        return filter

    def get_quarter_filter(self, start: str, end: str):
        start_date = datetime.strptime(start[:10], "%Y-%m-%d").date()
        end_date = datetime.strptime(end[:10], "%Y-%m-%d").date()

        def get_quarter_by_month(month):
            if month >= 1 and month <= 3:
                return "Q1"
            elif month >= 4 and month <= 6:
                return "Q2"
            elif month >= 7 and month <= 9:
                return "Q3"
            else:
                return "Q4"

        filter = []

        while start_date < end_date:
            filter.append(
                {
                    "and": [
                        {"period": {"eq": get_quarter_by_month(start_date.month)}},
                        {"year": {"eq": start_date.year}},
                    ]
                }
            )
            start_date = start_date + relativedelta(months=+1)

        return filter

    async def get_headcount(self, now):
        date_config = DateConfig().get_config(now)
        headcount_result = {}

        for date in date_config.keys():
            headcount_filter = {
                "and": [
                    {
                        "description": {
                            "in": [
                                "Contractor Headcount",
                                "Celanese Employee Headcount",
                            ]
                        }
                    },
                    {"period": {"eq": "Monthly"}},
                    {
                        "startDate": {
                            "gte": date_config[date]["start"][:10],
                            "lte": date_config[date]["end"][:10],
                        }
                    },
                ]
            }

            headcount_selection = """
            kpiValue
            externalId
            refSite {
                externalId
            }
            """

            headcount_kpi = await self.graphql_service.get_all_results_list(
                generate_query("listKPIManualInput", headcount_selection),
                "listKPIManualInput",
                headcount_filter,
            )

            print("Headcount - {}: {} results".format(date, len(headcount_kpi)))
            headcount_result[date] = headcount_kpi

        return headcount_result

    async def get_flawless_days(
        self, kpi_id: str, now, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        date_config = DateConfig().get_config(now)
        flawless_result_by_site = {}
        flawless_result_by_business_segment = {}
        sites_filter = self.get_sites_filter(sites, "refSite")

        for date in date_config.keys():
            flawless_filter = {
                "and": [
                    {"description": {"eq": "Flawless days"}},
                    {
                        "startDate": {
                            "gte": date_config[date]["start"][:10],
                            "lte": date_config[date]["end"][:10],
                        }
                    },
                    sites_filter,
                ]
            }

            flawless_selection = """
            kpiValue
            externalId
            refSite {
                externalId
            }
            refBusinessSegment {
                externalId
            }            
            """

            flawless_kpi = await self.graphql_service.get_all_results_list(
                generate_query("listKPIManualInput", flawless_selection),
                "listKPIManualInput",
                flawless_filter,
            )

            print("{} - {}: {} results".format(kpi_id, date, len(flawless_kpi)))
            flawless_result_by_site[date], flawless_result_by_business_segment[date] = (
                self.group_unit_sum(
                    flawless_kpi, "kpiValue", "refSite", "refBusinessSegment"
                )
            )

        return flawless_result_by_site, flawless_result_by_business_segment

    async def get_productivity(
        self, kpi_id: str, now, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        date_config = DateConfig().get_config(now)
        productivity_result_by_site = {}
        productivity_result_by_business_segment = {}
        year = ["lastTwoYear", "lastYear", "ytd"]
        months = ["actualMonth", "lastMonth", "lastTwoMonth", "lastThreeMonth"]

        sites_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            if date in year:
                productivity_result_by_site[date] = {}
                productivity_result_by_business_segment[date] = {}
                time_filter = self.get_productivity_time_filter(
                    kpi_id=kpi_id, date=date_config[date]["start"][:4]
                )
                productivity_filter = {
                    "and": [
                        {
                            "projectStatus": {
                                "in": [
                                    Productivity.ProjectStatus.COMMITTED.value,
                                    Productivity.ProjectStatus.CLOSED.value,
                                    Productivity.ProjectStatus.PLACEHOLDER.value,
                                ]
                            }
                        },
                        {
                            "contributionType": {
                                "in": [
                                    Productivity.ContributionType.COST_REDUCTION_YOY.value,
                                    Productivity.ContributionType.M_AND_A_CR_REV.value,
                                    Productivity.ContributionType.REVENUE_OPTIMIZATION.value,
                                ]
                            }
                        },
                        {
                            "functionCategory": {
                                "in": [
                                    Productivity.FunctionCategory.MANUFACTURING.value
                                ]
                            }
                        },
                        {
                            "not": {
                                "projectType": {
                                    "in": [
                                        Productivity.ProjectType.STRATEGIC_GROWTH.value
                                    ]
                                }
                            }
                        },
                        time_filter,
                        sites_filter,
                    ]
                }

                productivity_selection = """
                externalId
                projectName
                projectNumber
                projectStatus
                projectType
                functionCategory
                contributionType
                annualAmount
                janAmount
                febAmount
                marAmount
                aprAmount
                mayAmount
                junAmount
                julAmount
                augAmount
                sepAmount
                octAmount
                novAmount
                decAmount
                reportingSite {
                    externalId
                }
                businessSegment {
                    externalId
                }
                reportingUnits {
                    items {
                        externalId
                    }
                }
                """

                productivity_kpi = await self.graphql_service.get_all_results_list(
                    generate_query("listProductivity", productivity_selection),
                    "listProductivity",
                    productivity_filter,
                )

                print("{} - {}: {} results".format(kpi_id, date, len(productivity_kpi)))
                (
                    productivity_result_by_site[date],
                    productivity_result_by_business_segment[date],
                ) = self.group_unit_sum(
                    productivity_kpi, "annualAmount", "reportingSite", "businessSegment"
                )

                time_filter_future = self.get_productivity_time_filter(
                    kpi_id=kpi_id, date=str(int(date_config[date]["start"][:4]) + 1)
                )
                productivity_filter_future = {
                    "and": [
                        {
                            "projectStatus": {
                                "in": [
                                    Productivity.ProjectStatus.COMMITTED.value,
                                    Productivity.ProjectStatus.CLOSED.value,
                                    Productivity.ProjectStatus.PLACEHOLDER.value,
                                ]
                            }
                        },
                        {
                            "contributionType": {
                                "in": [
                                    Productivity.ContributionType.COST_REDUCTION_YOY.value,
                                    Productivity.ContributionType.M_AND_A_CR_REV.value,
                                    Productivity.ContributionType.REVENUE_OPTIMIZATION.value,
                                ]
                            }
                        },
                        {
                            "functionCategory": {
                                "in": [
                                    Productivity.FunctionCategory.MANUFACTURING.value
                                ]
                            }
                        },
                        {
                            "not": {
                                "projectType": {
                                    "in": [
                                        Productivity.ProjectType.STRATEGIC_GROWTH.value
                                    ]
                                }
                            }
                        },
                        time_filter_future,
                        sites_filter,
                    ]
                }

                productivity_kpi_future = (
                    await self.graphql_service.get_all_results_list(
                        generate_query("listProductivity", productivity_selection),
                        "listProductivity",
                        productivity_filter_future,
                    )
                )

                if date == "ytd":
                    (
                        productivity_result_by_site["january"],
                        productivity_result_by_business_segment["january"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "janAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["february"],
                        productivity_result_by_business_segment["february"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "febAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["march"],
                        productivity_result_by_business_segment["march"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "marAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["april"],
                        productivity_result_by_business_segment["april"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "aprAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["may"],
                        productivity_result_by_business_segment["may"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "mayAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["june"],
                        productivity_result_by_business_segment["june"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "junAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["july"],
                        productivity_result_by_business_segment["july"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "julAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["august"],
                        productivity_result_by_business_segment["august"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "augAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["september"],
                        productivity_result_by_business_segment["september"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "sepAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["october"],
                        productivity_result_by_business_segment["october"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "octAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["november"],
                        productivity_result_by_business_segment["november"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "novAmount",
                        "reportingSite",
                        "businessSegment",
                    )
                    (
                        productivity_result_by_site["december"],
                        productivity_result_by_business_segment["december"],
                    ) = self.group_unit_sum(
                        productivity_kpi,
                        "decAmount",
                        "reportingSite",
                        "businessSegment",
                    )

                for month in months:
                    if (
                        date_config[month]["start"][:4]
                        == date_config[date]["start"][:4]
                    ):
                        mounth_amount = self.get_mounth_amount(
                            date_config[month]["start"]
                        )
                        (
                            productivity_result_by_site[month],
                            productivity_result_by_business_segment[month],
                        ) = self.group_unit_sum(
                            productivity_kpi,
                            mounth_amount,
                            "reportingSite",
                            "businessSegment",
                        )
                    if date_config[month]["start"][:4] == str(
                        int(date_config[date]["start"][:4]) + 1
                    ):
                        mounth_amount = self.get_mounth_amount(
                            date_config[month]["start"]
                        )
                        (
                            productivity_result_by_site[month],
                            productivity_result_by_business_segment[month],
                        ) = self.group_unit_sum(
                            productivity_kpi_future,
                            mounth_amount,
                            "reportingSite",
                            "businessSegment",
                        )

        return productivity_result_by_site, productivity_result_by_business_segment

    def get_mounth_amount(self, date):
        mounth_date = datetime.strptime(date[:10], "%Y-%m-%d").date()
        return mounth_date.strftime("%b").lower() + "Amount"

    async def get_cost_variance(
        self, kpi_id: str, now, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        date_config = DateConfig().get_config(now)
        cost_variance_actual_by_site = {}
        cost_variance_actual_by_business_segment = {}
        cost_variance_forecast_by_site = {}
        cost_variance_forecast_by_business_segment = {}
        cost_variance_result_by_site = {}
        cost_variance_result_by_business_segment = {}
        sites_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            time_filter = self.get_range_time_filter(
                date_config[date]["start"], date_config[date]["end"]
            )

            cost_variance_filter_actual = {
                "and": [
                    {"currency": {"externalId": {"eq": "CUR-USD"}}},
                    {"costType": {"eq": "Actual"}},
                    {"or": time_filter},
                    sites_filter,
                ]
            }

            cost_variance_filter_forecast = {
                "and": [
                    {"currency": {"externalId": {"eq": "CUR-USD"}}},
                    {"costType": {"eq": "Forecast"}},
                    {"or": time_filter},
                    sites_filter,
                ]
            }

            cost_variance_selection = """
            externalId
            plantPeriodCost
            energyCost
            reportingSite {
                externalId
            }
            businessSegment {
                externalId
            }
            """

            cost_variance_kpi_actual = await self.graphql_service.get_all_results_list(
                generate_query("listCostVariance", cost_variance_selection),
                "listCostVariance",
                cost_variance_filter_actual,
            )

            cost_variance_kpi_forecast = (
                await self.graphql_service.get_all_results_list(
                    generate_query("listCostVariance", cost_variance_selection),
                    "listCostVariance",
                    cost_variance_filter_forecast,
                )
            )

            print(
                "{} - {}: Actual {} - Forecast {} results".format(
                    kpi_id,
                    date,
                    len(cost_variance_kpi_actual),
                    len(cost_variance_kpi_forecast),
                )
            )
            (
                cost_variance_actual_by_site[date],
                cost_variance_actual_by_business_segment[date],
            ) = self.get_sum_cost_variance(
                cost_variance_kpi_actual, "plantPeriodCost", "energyCost"
            )
            (
                cost_variance_forecast_by_site[date],
                cost_variance_forecast_by_business_segment[date],
            ) = self.get_sum_cost_variance(
                cost_variance_kpi_forecast, "plantPeriodCost", "energyCost"
            )
            (
                cost_variance_result_by_site[date],
                cost_variance_result_by_business_segment[date],
            ) = self.get_diff_cost_variance(
                cost_variance_kpi_actual,
                cost_variance_kpi_forecast,
                "plantPeriodCost",
                "energyCost",
            )

        return (
            cost_variance_actual_by_site,
            cost_variance_actual_by_business_segment,
            cost_variance_forecast_by_site,
            cost_variance_forecast_by_business_segment,
            cost_variance_result_by_site,
            cost_variance_result_by_business_segment,
        )

    async def get_volume_variance(
        self, kpi_id: str, now, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        date_config = DateConfig().get_config(now)
        volume_variance_actual_by_site = {}
        volume_variance_actual_by_business_segment = {}
        volume_variance_forecast_by_site = {}
        volume_variance_forecast_by_business_segment = {}
        volume_variance_result_by_site = {}
        volume_variance_result_by_business_segment = {}
        sites_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            time_filter = self.get_range_time_filter(
                date_config[date]["start"], date_config[date]["end"]
            )

            volume_variance_filter_actual = {
                "and": [
                    {"volumeUnitOfMeasurement": {"externalId": {"eq": "UOM_KGM"}}},
                    {"volumeType": {"eq": "Actual"}},
                    {"or": time_filter},
                    sites_filter,
                ]
            }

            volume_variance_filter_forecast = {
                "and": [
                    {"volumeUnitOfMeasurement": {"externalId": {"eq": "UOM_KGM"}}},
                    {"volumeType": {"eq": "Forecast"}},
                    {"or": time_filter},
                    sites_filter,
                ]
            }

            volume_variance_selection = """
            externalId
            ProductionVolume
            reportingSite {
                externalId
            }
            businessSegment {
                externalId
            }
            """

            volume_variance_kpi_actual = (
                await self.graphql_service.get_all_results_list(
                    generate_query("listVolumeVariance", volume_variance_selection),
                    "listVolumeVariance",
                    volume_variance_filter_actual,
                )
            )

            volume_variance_kpi_forecast = (
                await self.graphql_service.get_all_results_list(
                    generate_query("listVolumeVariance", volume_variance_selection),
                    "listVolumeVariance",
                    volume_variance_filter_forecast,
                )
            )

            print(
                "{} - {}: Actual {} - Forecast {} results".format(
                    kpi_id,
                    date,
                    len(volume_variance_kpi_actual),
                    len(volume_variance_kpi_forecast),
                )
            )
            (
                volume_variance_actual_by_site[date],
                volume_variance_actual_by_business_segment[date],
            ) = self.group_unit_sum(
                volume_variance_kpi_actual,
                "ProductionVolume",
                "reportingSite",
                "businessSegment",
            )
            (
                volume_variance_forecast_by_site[date],
                volume_variance_forecast_by_business_segment[date],
            ) = self.group_unit_sum(
                volume_variance_kpi_forecast,
                "ProductionVolume",
                "reportingSite",
                "businessSegment",
            )
            (
                volume_variance_result_by_site[date],
                volume_variance_result_by_business_segment[date],
            ) = self.group_unit_diff(
                volume_variance_kpi_actual,
                volume_variance_kpi_forecast,
                "ProductionVolume",
            )

        return (
            volume_variance_actual_by_site,
            volume_variance_actual_by_business_segment,
            volume_variance_forecast_by_site,
            volume_variance_forecast_by_business_segment,
            volume_variance_result_by_site,
            volume_variance_result_by_business_segment,
        )

    async def get_kg_actual_forecast(
        self, kpi_id: str, now, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        date_config = DateConfig().get_config(now)
        kg_actual_forecast_result_by_site = {}
        kg_actual_forecast_result_by_business_segment = {}
        sites_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            time_filter = self.get_range_time_filter(
                date_config[date]["start"], date_config[date]["end"]
            )

            kg_actual_forecast_filter = {
                "and": [
                    {"volumeUnitOfMeasurement": {"externalId": {"eq": "UOM_KGM"}}},
                    {"kpiFrequency": {"eq": "Month"}},
                    {"or": time_filter},
                    sites_filter,
                ]
            }

            kg_actual_forecast_selection = """
            externalId
            actualPlantPeriodCost
            actualEnergyCost
            actualProductionVolume
            forecastPlantPeriodCost
            forecastEnergyCost
            forecastProductionVolume
            reportingSite {
                externalId
            }
            businessSegment {
                externalId
            }
            """

            kg_actual_forecast_kpi = await self.graphql_service.get_all_results_list(
                generate_query("listDollarPerKG", kg_actual_forecast_selection),
                "listDollarPerKG",
                kg_actual_forecast_filter,
            )
            print(
                "{} - {}: {} results".format(kpi_id, date, len(kg_actual_forecast_kpi))
            )
            (
                kg_actual_forecast_result_by_site[date],
                kg_actual_forecast_result_by_business_segment[date],
            ) = self.group_unit_kg_actual_forecast(kg_actual_forecast_kpi)

        return (
            kg_actual_forecast_result_by_site,
            kg_actual_forecast_result_by_business_segment,
        )

    async def get_kg_headcount(
        self, kpi_id: str, now, headcount, sites
    ) -> tuple[dict[str, list[dict]], dict[str, list[dict]]]:
        date_config = DateConfig().get_config(now)
        kg_headcount_result_by_site = {}
        kg_headcount_result_by_business_segment = {}
        sites_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            kg_headcount_result_by_business_segment[date] = {}
            time_filter = self.get_range_time_filter(
                date_config[date]["start"], date_config[date]["end"]
            )

            kg_headcount_filter_actual = {
                "and": [
                    {"volumeUnitOfMeasurement": {"externalId": {"eq": "UOM_KGM"}}},
                    {"volumeType": {"eq": "Actual"}},
                    {"or": time_filter},
                    sites_filter,
                ]
            }

            kg_headcount_selection = """
            externalId
            ProductionVolume
            reportingSite {
                externalId
            }
            """

            kg_headcount_kpi_actual = await self.graphql_service.get_all_results_list(
                generate_query("listVolumeVariance", kg_headcount_selection),
                "listVolumeVariance",
                kg_headcount_filter_actual,
            )

            print(
                "{} - {}: {} results".format(kpi_id, date, len(kg_headcount_kpi_actual))
            )
            kg_headcount_result_by_site[date] = self.group_unit_div(
                kg_headcount_kpi_actual, headcount[date], "ProductionVolume", "kpiValue"
            )

        return kg_headcount_result_by_site, kg_headcount_result_by_business_segment

    async def get_cash_margin(
        self, now, sites
    ) -> tuple[
        dict[str, list[dict]],
        dict[str, list[dict]],
        dict[str, list[dict]],
        dict[str, list[dict]],
    ]:
        date_config = DateConfig().get_config(now)
        cash_margin_result_by_site = {}
        kg_cash_margin_result_by_site = {}
        cash_margin_result_by_business_segment = {}
        kg_cash_margin_result_by_business_segment = {}
        sites_filter = self.get_sites_filter(sites, "reportingSite")

        for date in date_config.keys():
            quarter_filter = self.get_quarter_filter(
                date_config[date]["start"], date_config[date]["end"]
            )

            cash_margin_filter_actual = {"and": [{"or": quarter_filter}, sites_filter]}

            cash_margin_selection = """
            externalId
            variableMargin
            fixedDistributionCost
            otherFixedCost
            plantPeriodCost
            energyCost
            salesVolume
            reportingSite {
                externalId
            }
            businessSegment {
                externalId
            }            
            """

            cash_margin_kpi = await self.graphql_service.get_all_results_list(
                generate_query("listCashMargin", cash_margin_selection),
                "listCashMargin",
                cash_margin_filter_actual,
            )

            print("Cash Margin - {}: {} results".format(date, len(cash_margin_kpi)))
            (
                cash_margin_result_by_site[date],
                cash_margin_result_by_business_segment[date],
                kg_cash_margin_result_by_site[date],
                kg_cash_margin_result_by_business_segment[date],
            ) = self.group_unit_cash_margin(cash_margin_kpi)

        return (
            cash_margin_result_by_site,
            cash_margin_result_by_business_segment,
            kg_cash_margin_result_by_site,
            kg_cash_margin_result_by_business_segment,
        )

    async def get_executive_kpi(self, now, headcount, sites):
        global_kpis_by_site = {}
        global_kpis_by_business_segment = {}

        kpis = ["GKPI-SOL-ACF"]
        for kpi in kpis:
            (
                global_kpis_by_site["GKPI-SOL-APC"],
                global_kpis_by_business_segment["GKPI-SOL-APC"],
                global_kpis_by_site["GKPI-SOL-FPC"],
                global_kpis_by_business_segment["GKPI-SOL-FPC"],
                global_kpis_by_site[kpi],
                global_kpis_by_business_segment[kpi],
            ) = await self.get_cost_variance(kpi_id=kpi, now=now, sites=sites)

        kpis = ["GKPI-SOL-APF"]
        for kpi in kpis:
            (
                global_kpis_by_site["GKPI-SOL-APV"],
                global_kpis_by_business_segment["GKPI-SOL-APV"],
                global_kpis_by_site["GKPI-SOL-FPV"],
                global_kpis_by_business_segment["GKPI-SOL-FPV"],
                global_kpis_by_site[kpi],
                global_kpis_by_business_segment[kpi],
            ) = await self.get_volume_variance(kpi_id=kpi, now=now, sites=sites)

        kpis = ["GKPI-SOL-KGH"]  # nao tem aqui e no flawless
        for kpi in kpis:
            (
                kpi_result_by_site,
                kpi_result_by_business_segment,
            ) = await self.get_kg_headcount(
                kpi_id=kpi, now=now, headcount=headcount, sites=sites
            )
            global_kpis_by_site[kpi] = kpi_result_by_site
            global_kpis_by_business_segment[kpi] = kpi_result_by_business_segment

        kpis = ["GKPI-SOL-AOP"]
        for kpi in kpis:
            (
                kpi_result_by_site,
                kpi_result_by_business_segment,
            ) = await self.get_kg_actual_forecast(kpi_id=kpi, now=now, sites=sites)
            global_kpis_by_site[kpi] = kpi_result_by_site
            global_kpis_by_business_segment[kpi] = kpi_result_by_business_segment

        (
            global_kpis_by_site["GKPI-SOL-PCM"],
            global_kpis_by_business_segment["GKPI-SOL-PCM"],
            global_kpis_by_site["GKPI-SOL-MKG"],
            global_kpis_by_business_segment["GKPI-SOL-MKG"],
        ) = await self.get_cash_margin(now=now, sites=sites)

        return global_kpis_by_site, global_kpis_by_business_segment

    async def get_foundational_productivity_kpi(self, now, sites: list[dict], kpi: str):
        global_kpis_by_site = {}
        global_kpis_by_business_segment = {}
        (
            kpi_result_by_site,
            kpi_result_by_business_segment,
        ) = await self.get_productivity(kpi_id=kpi, now=now, sites=sites)
        global_kpis_by_site[kpi] = kpi_result_by_site
        global_kpis_by_business_segment[kpi] = kpi_result_by_business_segment

        return global_kpis_by_site, global_kpis_by_business_segment

    async def get_gkpisol_kpi(self, now, sites):
        global_kpis_by_site = {}
        global_kpis_by_business_segment = {}

        kpis = ["GKPI-SOL-FLD"]
        for kpi in kpis:
            (
                kpi_result_by_site,
                kpi_result_by_business_segment,
            ) = await self.get_flawless_days(kpi_id=kpi, now=now, sites=sites)
            global_kpis_by_site[kpi] = kpi_result_by_site
            global_kpis_by_business_segment[kpi] = kpi_result_by_business_segment

        return global_kpis_by_site, global_kpis_by_business_segment
