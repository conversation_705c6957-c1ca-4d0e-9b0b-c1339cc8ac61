from datetime import datetime, timedelta, timezone


class DateFilter:
    start = ""
    end = ""

    def __init__(self, start, end):
        self.start = start
        self.end = end

    def show_start_end(self):
        print("Start: {}\nEnd: {}".format(self.start, self.end))


class DateConfig:
    def get_config(self, now: datetime) -> dict[str, dict]:
        january_rule_year = (
            (now.year - 1)
            if now.month == 1 or (now.month == 2 and now.day < 11)
            else now.year
        )
        january_rule_month = (
            12 if now.month == 1 or (now.month == 2 and now.day < 11) else now.month
        )
        january_rule_day = (
            31 if now.month == 1 or (now.month == 2 and now.day < 11) else now.day
        )
        return {
            "lastTwoYear": {
                "start": datetime(year=january_rule_year - 2, month=1, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year - 2,
                    month=12,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "lastYear": {
                "start": datetime(year=january_rule_year - 1, month=1, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year - 1,
                    month=12,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "ytd": {
                "start": datetime(year=january_rule_year, month=1, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=january_rule_month,
                    day=january_rule_day,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "january": {
                "start": datetime(year=january_rule_year, month=1, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=1,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "february": {
                "start": datetime(year=january_rule_year, month=2, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=2,
                    day=29 if now.year % 4 == 0 else 28,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "march": {
                "start": datetime(year=january_rule_year, month=3, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=3,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "april": {
                "start": datetime(year=january_rule_year, month=4, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=4,
                    day=30,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "may": {
                "start": datetime(year=january_rule_year, month=5, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=5,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "june": {
                "start": datetime(year=january_rule_year, month=6, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=6,
                    day=30,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "july": {
                "start": datetime(year=january_rule_year, month=7, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=7,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "august": {
                "start": datetime(year=january_rule_year, month=8, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=8,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "september": {
                "start": datetime(year=january_rule_year, month=9, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=9,
                    day=30,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "october": {
                "start": datetime(year=january_rule_year, month=10, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=10,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "november": {
                "start": datetime(year=january_rule_year, month=11, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=11,
                    day=30,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "december": {
                "start": datetime(year=january_rule_year, month=12, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=january_rule_year,
                    month=12,
                    day=31,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "actualMonth": {
                "start": datetime(year=now.year, month=now.month, day=1)
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=now.year,
                    month=now.month,
                    day=now.day,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "lastMonth": {
                "start": datetime(
                    year=now.year if now.month - 1 > 0 else now.year - 1,
                    month=now.month - 1 if now.month - 1 > 0 else 12 + (now.month - 1),
                    day=1,
                )
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=now.year if now.month - 1 > 0 else now.year - 1,
                    month=now.month - 1 if now.month - 1 > 0 else 12 + (now.month - 1),
                    day=(
                        datetime(year=now.year, month=now.month, day=1)
                        - timedelta(days=1)
                    ).day,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "lastTwoMonth": {
                "start": datetime(
                    year=now.year if now.month - 2 > 0 else now.year - 1,
                    month=now.month - 2 if now.month - 2 > 0 else 12 + (now.month - 2),
                    day=1,
                )
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=now.year if now.month - 2 > 0 else now.year - 1,
                    month=now.month - 2 if now.month - 2 > 0 else 12 + (now.month - 2),
                    day=(
                        datetime(
                            year=now.year if now.month - 1 > 0 else now.year - 1,
                            month=now.month - 1
                            if now.month - 1 > 0
                            else 12 + (now.month - 1),
                            day=1,
                        )
                        - timedelta(days=1)
                    ).day,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
            "lastThreeMonth": {
                "start": datetime(
                    year=now.year if now.month - 3 > 0 else now.year - 1,
                    month=now.month - 3 if now.month - 3 > 0 else 12 + (now.month - 3),
                    day=1,
                )
                .replace(microsecond=0)
                .isoformat(),
                "end": datetime(
                    year=now.year if now.month - 3 > 0 else now.year - 1,
                    month=now.month - 3 if now.month - 3 > 0 else 12 + (now.month - 3),
                    day=(
                        datetime(
                            year=now.year if now.month - 2 > 0 else now.year - 1,
                            month=now.month - 2
                            if now.month - 2 > 0
                            else 12 + (now.month - 2),
                            day=1,
                        )
                        - timedelta(days=1)
                    ).day,
                    hour=23,
                    minute=59,
                    second=59,
                )
                .replace(microsecond=0)
                .isoformat(),
            },
        }
