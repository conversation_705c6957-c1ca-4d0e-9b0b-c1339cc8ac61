import os
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from infra.projects import CogniteProjectsDev
from dotenv import load_dotenv, dotenv_values

load_dotenv()
config = dotenv_values(".env")
config_dev = dotenv_values(".env.dev")

function_env = os.getenv("FUNCTION_ENV") or "dev"

model_config = SettingsConfigDict(extra="ignore")

class AuthVariables(BaseSettings):
    model_config = model_config

    client_id: str = config["AUTH_CLIENT_ID"]
    tenant_id: str = config["AUTH_TENANT_ID"]
    secret: str = config["AUTH_SECRET"]
    scopes_str: str = Field(alias="auth_scopes", default=config["AUTH_SCOPES"])
    token_uri: str = config["AUTH_TOKEN_URI"]
    token_override: Optional[str] = config["AUTH_TOKEN_OVERRIDE"]

    @property
    def scopes(self) -> List[str]:
        return self.scopes_str.split(" ")


class CogniteVariables(BaseSettings):
    model_config = model_config
    base_uri: str = config["COGNITE_BASE_URI"]
    client_name: str = config["COGNITE_CLIENT_NAME"]
    project: CogniteProjectsDev = config_dev["COGNITE_PROJECT"]
    graphql_uri_raw: str = Field(
        alias="cognite_graphql_uri", default=config["COGNITE_GRAPHQL_URI"]
    )
    graphql_model_space_kpi: str = config["COGNITE_GRAPHQL_MODEL_SPACE_KPI"]
    graphql_model_external_id_kpi: str = config["COGNITE_GRAPHQL_MODEL_EXTERNAL_ID_KPI"]
    graphql_model_version_kpi: str = config_dev["COGNITE_GRAPHQL_MODEL_VERSION_KPI"]
    graphql_model_space_asset: str = config["COGNITE_GRAPHQL_MODEL_SPACE_ASSET"]
    graphql_model_external_id_asset: str = config[
        "COGNITE_GRAPHQL_MODEL_EXTERNAL_ID_ASSET"
    ]
    graphql_model_version_asset: str = config_dev[
        "COGNITE_GRAPHQL_MODEL_VERSION_ASSET"
    ]
    graphql_model_space_stewardship: str = config[
        "COGNITE_GRAPHQL_MODEL_SPACE_STEWARDSHIP"
    ]
    graphql_model_external_id_stewardship: str = config[
        "COGNITE_GRAPHQL_MODEL_EXTERNAL_ID_STEWARDSHIP"
    ]
    graphql_model_version_stewardship: str = config_dev[
        "COGNITE_GRAPHQL_MODEL_VERSION_STEWARDSHIP"
    ]
    graphql_model_space_incident: str = config["COGNITE_GRAPHQL_MODEL_SPACE_INCIDENT"]
    graphql_model_external_id_incident: str = config[
        "COGNITE_GRAPHQL_MODEL_EXTERNAL_ID_INCIDENT"
    ]
    graphql_model_version_incident: str = config_dev[
        "COGNITE_GRAPHQL_MODEL_VERSION_INCIDENT"
    ]
    graphql_model_space_executive: str = config["COGNITE_GRAPHQL_MODEL_SPACE_EXECUTIVE"]
    graphql_model_external_id_executive: str = config[
        "COGNITE_GRAPHQL_MODEL_EXTERNAL_ID_EXECUTIVE"
    ]
    graphql_model_version_executive: str = config_dev[
        "COGNITE_GRAPHQL_MODEL_VERSION_EXECUTIVE"
    ]
    graphql_model_space_gkpisol: str = config["COGNITE_GRAPHQL_MODEL_SPACE_GKPISOL"]
    graphql_model_external_id_gkpisol: str = config[
        "COGNITE_GRAPHQL_MODEL_EXTERNAL_ID_GKPISOL"
    ]
    graphql_model_version_gkpisol: str = config_dev[
        "COGNITE_GRAPHQL_MODEL_VERSION_GKPISOL"
    ]
    graphql_model_space_ofwasol: str = config["COGNITE_GRAPHQL_MODEL_SPACE_OFWASOL"]
    graphql_model_external_id_ofwasol: str = config[
        "COGNITE_GRAPHQL_MODEL_EXTERNAL_ID_OFWASOL"
    ]
    graphql_model_version_ofwasol: str = config_dev[
        "COGNITE_GRAPHQL_MODEL_VERSION_OFWASOL"
    ]
    graphql_model_space_quality: str = config["COGNITE_GRAPHQL_MODEL_SPACE_QUALITY"]
    graphql_model_external_id_quality: str = config[
        "COGNITE_GRAPHQL_MODEL_EXTERNAL_ID_QUALITY"
    ]
    graphql_model_version_quality: str = config_dev[
        "COGNITE_GRAPHQL_MODEL_VERSION_QUALITY"
    ]

    @property
    def graphql_uri_kpi(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.base_uri)
            .replace("@project", self.project)
            .replace("@model_space", self.graphql_model_space_kpi)
            .replace("@model_external_id", self.graphql_model_external_id_kpi)
            .replace("@model_version", self.graphql_model_version_kpi)
        )

    @property
    def graphql_uri_asset(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.base_uri)
            .replace("@project", self.project)
            .replace("@model_space", self.graphql_model_space_asset)
            .replace("@model_external_id", self.graphql_model_external_id_asset)
            .replace("@model_version", self.graphql_model_version_asset)
        )

    @property
    def graphql_uri_stewardship(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.base_uri)
            .replace("@project", self.project)
            .replace("@model_space", self.graphql_model_space_stewardship)
            .replace("@model_external_id", self.graphql_model_external_id_stewardship)
            .replace("@model_version", self.graphql_model_version_stewardship)
        )

    @property
    def graphql_uri_trir(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.base_uri)
            .replace("@project", self.project)
            .replace("@model_space", self.graphql_model_space_incident)
            .replace("@model_external_id", self.graphql_model_external_id_incident)
            .replace("@model_version", self.graphql_model_version_incident)
        )

    @property
    def graphql_uri_executive(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.base_uri)
            .replace("@project", self.project)
            .replace("@model_space", self.graphql_model_space_executive)
            .replace("@model_external_id", self.graphql_model_external_id_executive)
            .replace("@model_version", self.graphql_model_version_executive)
        )

    @property
    def graphql_uri_gkpisol(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.base_uri)
            .replace("@project", self.project)
            .replace("@model_space", self.graphql_model_space_gkpisol)
            .replace("@model_external_id", self.graphql_model_external_id_gkpisol)
            .replace("@model_version", self.graphql_model_version_gkpisol)
        )

    @property
    def graphql_uri_ofwasol(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.base_uri)
            .replace("@project", self.project)
            .replace("@model_space", self.graphql_model_space_ofwasol)
            .replace("@model_external_id", self.graphql_model_external_id_ofwasol)
            .replace("@model_version", self.graphql_model_version_ofwasol)
        )

    @property
    def graphql_uri_quality(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.base_uri)
            .replace("@project", self.project)
            .replace("@model_space", self.graphql_model_space_quality)
            .replace("@model_external_id", self.graphql_model_external_id_quality)
            .replace("@model_version", self.graphql_model_version_quality)
        )


class EnvVariables:
    def __init__(self) -> None:
        self.auth = AuthVariables()
        self.cognite = CogniteVariables()
