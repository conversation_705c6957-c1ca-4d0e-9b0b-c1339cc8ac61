from pathlib import Path
from typing import Any
from datetime import datetime

import random
from cognite.client import CogniteClient
from infra.env_variables import CogniteVariables
from data.get_data import get_raw_data

from infra.cognite_client_factory import CogniteClientFactory
from infra.env_variables import EnvVariables
from infra.env_variables_qa import EnvVariablesQA
from infra.env_variables_prod import EnvVariablesPROD

from cognite.client.data_classes.data_modeling import (
    NodeApply,
    NodeOrEdgeData,
    ViewId,
)


class GlobalKPIService:
    def __init__(self, client: CogniteClient, cognite: CogniteVariables):
        self.client = client
        self.cognite = cognite

    def check_global_kpi_model(
        self,
    ):
        return ""

    async def update_global_kpis(
        self,
        kpis_by_sites: dict[str, list[dict]],
        kpis_by_business_segment: dict[str, list[dict]],
        sites: list,
        business_segments_per_site: dict[str, set()],
    ):
        kpi = await self.insert_kpis_config()

        global_kpis_by_site = []
        global_kpis_by_business_segment = []
        global_kpi_value = {}

        for key, values in kpis_by_sites.items():
            for site in sites:
                global_kpi_value = {
                    "externalId": key + "-" + site["siteCode"],
                    "refGlobalVisionKPI": {
                        "externalId": key,
                        "space": "GKPI-COR-ALL-DAT",
                    },
                    "refReportingSite": {
                        "externalId": site["externalId"],
                        "space": "REF-COR-ALL-DAT",
                    },
                    "lastTwoYear": values["lastTwoYear"].get(site["externalId"], 0),
                    "lastYear": values["lastYear"].get(site["externalId"], 0),
                    "ytd": values["ytd"].get(site["externalId"], 0),
                    "lastThreeMonth": values["lastThreeMonth"].get(
                        site["externalId"], 0
                    ),
                    "lastTwoMonth": values["lastTwoMonth"].get(site["externalId"], 0),
                    "lastMonth": values["lastMonth"].get(site["externalId"], 0),
                    "actualMonth": values["actualMonth"].get(site["externalId"], 0),
                    "january": values["january"].get(site["externalId"], 0),
                    "february": values["february"].get(site["externalId"], 0),
                    "march": values["march"].get(site["externalId"], 0),
                    "april": values["april"].get(site["externalId"], 0),
                    "may": values["may"].get(site["externalId"], 0),
                    "june": values["june"].get(site["externalId"], 0),
                    "july": values["july"].get(site["externalId"], 0),
                    "august": values["august"].get(site["externalId"], 0),
                    "september": values["september"].get(site["externalId"], 0),
                    "october": values["october"].get(site["externalId"], 0),
                    "november": values["november"].get(site["externalId"], 0),
                    "december": values["december"].get(site["externalId"], 0),
                }
                global_kpis_by_site.append(global_kpi_value)

        for key, values in kpis_by_business_segment.items():
            for site in sites:
                for business_segment in business_segments_per_site:
                    global_kpi_value = {
                        "externalId": key
                        + "-"
                        + site["siteCode"]
                        + "-"
                        + business_segment["code"],
                        "refGlobalVisionKPI": {
                            "externalId": key,
                            "space": "GKPI-COR-ALL-DAT",
                        },
                        "refReportingSite": {
                            "externalId": site["externalId"],
                            "space": "REF-COR-ALL-DAT",
                        },
                        "refBusinessSegment": {
                            "externalId": business_segment["externalId"],
                            "space": "REF-COR-ALL-DAT",
                        },
                        "lastTwoYear": values["lastTwoYear"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "lastYear": values["lastYear"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "ytd": values["ytd"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "lastThreeMonth": values["lastThreeMonth"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "lastTwoMonth": values["lastTwoMonth"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "lastMonth": values["lastMonth"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "actualMonth": values["actualMonth"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "january": values["january"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "february": values["february"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "march": values["march"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "april": values["april"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "may": values["may"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "june": values["june"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "july": values["july"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "august": values["august"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "september": values["september"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "october": values["october"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "november": values["november"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                        "december": values["december"]
                        .get(site["externalId"], {})
                        .get(business_segment["externalId"], 0),
                    }
                    global_kpis_by_business_segment.append(global_kpi_value)

        gvk_aggregation_version = await self.get_latest_view_version(
            self.cognite.graphql_model_space_gkpisol, "GlobalVisionKPIAggregation"
        )
        gvk_business_segment_aggregation_version = await self.get_latest_view_version(
            self.cognite.graphql_model_space_gkpisol,
            "GlobalVisionKPIBusinessSegmentAggregation",
        )

        entries_by_site = [
            NodeApply(
                space="GKPI-COR-ALL-DAT",
                external_id=global_kpi["externalId"],
                sources=[
                    NodeOrEdgeData(
                        source=ViewId(
                            space=self.cognite.graphql_model_space_gkpisol,
                            external_id="GlobalVisionKPIAggregation",
                            version=gvk_aggregation_version,
                        ),
                        properties={
                            k: v for k, v in global_kpi.items() if k != "externalId"
                        },
                    )
                ],
            )
            for global_kpi in global_kpis_by_site
        ]

        entries_by_business_segment = [
            NodeApply(
                space="GKPI-COR-ALL-DAT",
                external_id=global_kpi["externalId"],
                sources=[
                    NodeOrEdgeData(
                        source=ViewId(
                            space=self.cognite.graphql_model_space_gkpisol,
                            external_id="GlobalVisionKPIBusinessSegmentAggregation",
                            version=gvk_business_segment_aggregation_version,
                        ),
                        properties={
                            k: v for k, v in global_kpi.items() if k != "externalId"
                        },
                    )
                ],
            )
            for global_kpi in global_kpis_by_business_segment
        ]

        self.client.data_modeling.instances.apply(nodes=entries_by_site)
        self.client.data_modeling.instances.apply(nodes=entries_by_business_segment)
        return (
            "entries_by_site: ",
            len(entries_by_site),
            " entries_by_business_segment: ",
            len(entries_by_business_segment),
        )

    async def insert_kpis_config(self):
        gvk_version = await self.get_latest_view_version(
            self.cognite.graphql_model_space_gkpisol, "GlobalVisionKPI"
        )

        has_kpis = self.client.data_modeling.instances.list(
            sources=[
                (
                    self.cognite.graphql_model_space_gkpisol,
                    "GlobalVisionKPI",
                    gvk_version,
                )
            ],
            limit=None,
        )

        if len(has_kpis) > 0:
            return len(has_kpis)

        # read the json
        raw_data = get_raw_data(instance_space="GKPI-COR-ALL-DAT")

        kpis_groups = raw_data["KPIGroup"]
        kpis = raw_data["KPI"]

        gvk_group_version = await self.get_latest_view_version(
            self.cognite.graphql_model_space_gkpisol, "GlobalVisionKPIGroup"
        )

        entry_groups = [
            NodeApply(
                space="GKPI-COR-ALL-DAT",
                external_id=kpis_group["externalId"],
                sources=[
                    NodeOrEdgeData(
                        source=ViewId(
                            space=self.cognite.graphql_model_space_gkpisol,
                            external_id="GlobalVisionKPIGroup",
                            version=gvk_group_version,
                        ),
                        properties={
                            k: v for k, v in kpis_group.items() if k != "externalId"
                        },
                    )
                ],
            )
            for kpis_group in kpis_groups
        ]

        entry_kpis = [
            NodeApply(
                space="GKPI-COR-ALL-DAT",
                external_id=kpi["externalId"],
                sources=[
                    NodeOrEdgeData(
                        source=ViewId(
                            space=self.cognite.graphql_model_space_gkpisol,
                            external_id="GlobalVisionKPI",
                            version=gvk_version,
                        ),
                        properties={k: v for k, v in kpi.items() if k != "externalId"},
                    )
                ],
            )
            for kpi in kpis
        ]

        self.client.data_modeling.instances.apply(nodes=entry_groups)
        self.client.data_modeling.instances.apply(nodes=entry_kpis)

        return len(entry_kpis)

    async def update_targets(self, reporting_sites):
        gvk_version = await self.get_latest_view_version(
            self.cognite.graphql_model_space_gkpisol, "GlobalVisionKPI"
        )

        kpis = self.client.data_modeling.instances.list(
            sources=[
                (
                    self.cognite.graphql_model_space_gkpisol,
                    "GlobalVisionKPI",
                    gvk_version,
                )
            ],
            limit=None,
        )

        targets = []
        target_instance = {}
        year = datetime.now().year
        for kpi in kpis.data:
            for site in reporting_sites:
                target_instance = {
                    "externalId": kpi.external_id
                    + "-"
                    + site["siteCode"]
                    + "-"
                    + str(year),
                    "refGlobalVisionKPI": {
                        "externalId": kpi.external_id,
                        "space": "GKPI-COR-ALL-DAT",
                    },
                    "refReportingSite": {
                        "externalId": site["externalId"],
                        "space": "REF-COR-ALL-DAT",
                    },
                    "rangeDirection": "",
                    "value": 0,
                    "year": year,
                }

                targets.append(target_instance)

        gvk_target_version = await self.get_latest_view_version(
            self.cognite.graphql_model_space_gkpisol, "GlobalVisionKPITarget"
        )

        entry_targets = [
            NodeApply(
                space="GKPI-COR-ALL-DAT",
                external_id=target["externalId"],
                sources=[
                    NodeOrEdgeData(
                        source=ViewId(
                            space=self.cognite.graphql_model_space_gkpisol,
                            external_id="GlobalVisionKPITarget",
                            version=gvk_target_version,
                        ),
                        properties={
                            k: v for k, v in target.items() if k != "externalId"
                        },
                    )
                ],
            )
            for target in targets
        ]

        self.client.data_modeling.instances.apply(nodes=entry_targets)
        return len(entry_targets)

    async def get_latest_view_version(self, space: str, view: str):
        views = self.client.data_modeling.views.retrieve((space, view))
        views.data.sort(key=lambda x: x.__getattribute__("created_time"))
        version = views[-1].version
        return version
