# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: data_points.proto
# Protobuf Python Version: 4.25.4
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x64\x61ta_points.proto\x12\x1f\x63om.cognite.v1.timeseries.proto\"&\n\x06Status\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x03\x12\x0e\n\x06symbol\x18\x02 \x01(\t\"\x80\x01\n\x10NumericDatapoint\x12\x11\n\ttimestamp\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x37\n\x06status\x18\x03 \x01(\x0b\x32\'.com.cognite.v1.timeseries.proto.Status\x12\x11\n\tnullValue\x18\x04 \x01(\x08\"Z\n\x11NumericDatapoints\x12\x45\n\ndatapoints\x18\x01 \x03(\x0b\x32\x31.com.cognite.v1.timeseries.proto.NumericDatapoint\"\x7f\n\x0fStringDatapoint\x12\x11\n\ttimestamp\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\t\x12\x37\n\x06status\x18\x03 \x01(\x0b\x32\'.com.cognite.v1.timeseries.proto.Status\x12\x11\n\tnullValue\x18\x04 \x01(\x08\"X\n\x10StringDatapoints\x12\x44\n\ndatapoints\x18\x01 \x03(\x0b\x32\x30.com.cognite.v1.timeseries.proto.StringDatapoint\"\xf1\x02\n\x12\x41ggregateDatapoint\x12\x11\n\ttimestamp\x18\x01 \x01(\x03\x12\x0f\n\x07\x61verage\x18\x02 \x01(\x01\x12\x0b\n\x03max\x18\x03 \x01(\x01\x12\x0b\n\x03min\x18\x04 \x01(\x01\x12\r\n\x05\x63ount\x18\x05 \x01(\x01\x12\x0b\n\x03sum\x18\x06 \x01(\x01\x12\x15\n\rinterpolation\x18\x07 \x01(\x01\x12\x19\n\x11stepInterpolation\x18\x08 \x01(\x01\x12\x1a\n\x12\x63ontinuousVariance\x18\t \x01(\x01\x12\x18\n\x10\x64iscreteVariance\x18\n \x01(\x01\x12\x16\n\x0etotalVariation\x18\x0b \x01(\x01\x12\x11\n\tcountGood\x18\x0c \x01(\x01\x12\x16\n\x0e\x63ountUncertain\x18\r \x01(\x01\x12\x10\n\x08\x63ountBad\x18\x0e \x01(\x01\x12\x14\n\x0c\x64urationGood\x18\x0f \x01(\x01\x12\x19\n\x11\x64urationUncertain\x18\x10 \x01(\x01\x12\x13\n\x0b\x64urationBad\x18\x11 \x01(\x01\"^\n\x13\x41ggregateDatapoints\x12G\n\ndatapoints\x18\x01 \x03(\x0b\x32\x33.com.cognite.v1.timeseries.proto.AggregateDatapoint\"/\n\nInstanceId\x12\r\n\x05space\x18\x01 \x01(\t\x12\x12\n\nexternalId\x18\x02 \x01(\tB\x02P\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'data_points_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'P\001'
  _globals['_STATUS']._serialized_start=54
  _globals['_STATUS']._serialized_end=92
  _globals['_NUMERICDATAPOINT']._serialized_start=95
  _globals['_NUMERICDATAPOINT']._serialized_end=223
  _globals['_NUMERICDATAPOINTS']._serialized_start=225
  _globals['_NUMERICDATAPOINTS']._serialized_end=315
  _globals['_STRINGDATAPOINT']._serialized_start=317
  _globals['_STRINGDATAPOINT']._serialized_end=444
  _globals['_STRINGDATAPOINTS']._serialized_start=446
  _globals['_STRINGDATAPOINTS']._serialized_end=534
  _globals['_AGGREGATEDATAPOINT']._serialized_start=537
  _globals['_AGGREGATEDATAPOINT']._serialized_end=906
  _globals['_AGGREGATEDATAPOINTS']._serialized_start=908
  _globals['_AGGREGATEDATAPOINTS']._serialized_end=1002
  _globals['_INSTANCEID']._serialized_start=1004
  _globals['_INSTANCEID']._serialized_end=1051
# @@protoc_insertion_point(module_scope)
